(()=>{"use strict";var e={6262:(e,n)=>{n.A=(e,n)=>{const t=e.__vccOpts||e;for(const[e,r]of n)t[e]=r;return t}}},n={};const t=Vue;var r={class:"card"},o={class:"card-header"},a={class:"card-title"},i={class:"card-body",ref:"calendar"};function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,t="function"==typeof Symbol?Symbol:{},r=t.iterator||"@@iterator",o=t.toStringTag||"@@toStringTag";function a(t,r,o,a){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return u(s,"_invoke",function(t,r,o){var a,c,u,l=0,s=o||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(n,t){return a=n,c=0,u=e,f.n=t,i}};function v(t,r){for(c=t,u=r,n=0;!d&&l&&!o&&n<s.length;n++){var o,a=s[n],v=f.p,p=a[2];t>3?(o=p===r)&&(u=a[(c=a[4])?5:(c=3,3)],a[4]=a[5]=e):a[0]<=v&&((o=t<2&&v<a[1])?(c=0,f.v=r,f.n=a[1]):v<p&&(o=t<3||a[0]>r||r>p)&&(a[4]=t,a[5]=r,f.n=p,c=0))}if(o||t>1)return i;throw d=!0,r}return function(o,s,p){if(l>1)throw TypeError("Generator is already running");for(d&&1===s&&v(s,p),c=s,u=p;(n=c<2?e:u)||!d;){a||(c?c<3?(c>1&&(f.n=-1),v(c,u)):f.n=u:f.v=u);try{if(l=2,a){if(c||(o="next"),n=a[o]){if(!(n=n.call(a,u)))throw TypeError("iterator result is not an object");if(!n.done)return n;u=n.value,c<2&&(c=0)}else 1===c&&(n=a.return)&&n.call(a),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);a=e}else if((n=(d=f.n<0)?u:t.call(r,f))!==i)break}catch(n){a=e,c=1,u=n}finally{l=1}}return{value:n,done:d}}}(t,o,a),!0),s}var i={};function l(){}function s(){}function d(){}n=Object.getPrototypeOf;var f=[][r]?n(n([][r]())):(u(n={},r,function(){return this}),n),v=d.prototype=l.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,u(e,o,"GeneratorFunction")),e.prototype=Object.create(v),e}return s.prototype=d,u(v,"constructor",d),u(d,"constructor",s),s.displayName="GeneratorFunction",u(d,o,"GeneratorFunction"),u(v),u(v,o,"Generator"),u(v,r,function(){return this}),u(v,"toString",function(){return"[object Generator]"}),(c=function(){return{w:a,m:p}})()}function u(e,n,t,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}u=function(e,n,t,r){if(n)o?o(e,n,{value:t,enumerable:!r,configurable:!r,writable:!r}):e[n]=t;else{var a=function(n,t){u(e,n,function(e){return this._invoke(n,t,e)})};a("next",0),a("throw",1),a("return",2)}},u(e,n,t,r)}function l(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void t(e)}c.done?n(u):Promise.resolve(u).then(r,o)}const s={props:{eventsUrl:{type:String,required:!0}},data:function(){return{calendarInstance:null,booking:null,loading:!0}},mounted:function(){var e,n=this;return(e=c().m(function e(){return c().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,n.$nextTick();case 1:n.calendarInstance&&calendarInstance.destroy(),n.$refs.calendar&&(n.calendarInstance=new FullCalendar.Calendar(n.$refs.calendar,{fixedWeekCount:!1,headerToolbar:{left:"title"},navLinks:!0,editable:!1,dayMaxEvents:!0,events:{url:n.eventsUrl},loading:function(e){n.loading=e},eventClick:function(e){n.booking=e.event.extendedProps.detail,$("#view-booking-event-link").attr("href",e.event.extendedProps.detailUrl),$("#view-booking-event").modal("show")}}),n.calendarInstance.render());case 2:return e.a(2)}},e)}),function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){l(a,r,o,i,c,"next",e)}function c(e){l(a,r,o,i,c,"throw",e)}i(void 0)})})()}};var d=function t(r){var o=n[r];if(void 0!==o)return o.exports;var a=n[r]={exports:{}};return e[r](a,a.exports,t),a.exports}(6262);const f=(0,d.A)(s,[["render",function(e,n,c,u,l,s){return(0,t.openBlock)(),(0,t.createElementBlock)("div",r,[(0,t.createElementVNode)("div",o,[(0,t.createElementVNode)("h4",a,[(0,t.renderSlot)(e.$slots,"title")])]),(0,t.createElementVNode)("div",i,null,512),l.loading?(0,t.renderSlot)(e.$slots,"loading",{key:0}):(0,t.createCommentVNode)("",!0),(0,t.renderSlot)(e.$slots,"event",{booking:l.booking})])}]]);"undefined"!=typeof vueApp&&vueApp.booting(function(e){e.component("calendar-booking-reports-component",f)})})();