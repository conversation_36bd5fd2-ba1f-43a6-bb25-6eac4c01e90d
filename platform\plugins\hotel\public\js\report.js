(()=>{"use strict";var t={6262:(t,e)=>{e.A=(t,e)=>{const r=t.__vccOpts||t;for(const[t,a]of e)r[t]=a;return r}}},e={};const r=Vue;var a={key:0,class:"btn-group d-block text-end"},n={class:"btn btn-sm btn-secondary",href:"javascript:","data-bs-toggle":"dropdown","aria-expanded":"false"},o={class:"dropdown-menu float-end"},i=["onClick"],l={key:1,class:"row px-3"},c={class:"col-12"},s={class:"list-unstyled"};const u={props:{url:{type:String,default:null,required:!0},date_from:{type:String,default:null,required:!0},date_to:{type:String,default:null,required:!0},format:{type:String,default:"dd/MM/yy",required:!1},filters:{type:Array,default:function(){return[]},required:!1},filterDefault:{type:String,default:"",required:!1}},data:function(){return{isLoading:!0,earningSales:[],colors:["#fcb800","#80bc00"],chart:null,filtering:"",chartFromDate:null,chartToDate:null}},mounted:function(){var t=this;this.setFiltering(),this.chartFromDate=this.date_from,this.chartToDate=this.date_to,this.renderChart(),$event.on("sales-report-chart:reload",function(e){t.chartFromDate=e.date_from,t.chartToDate=e.date_to,t.renderChart()})},methods:{setFiltering:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(t||(t=this.filterDefault),this.filters.length){var e=this.filters.find(function(e){return e.key==t});this.filtering=e?e.text:t}},renderChart:function(){var t=this;this.url&&axios.get(this.url+"?date_from="+this.chartFromDate+"&date_to="+this.chartToDate).then(function(e){if(e.data.error)Botble.showError(e.data.message);else{t.earningSales=e.data.data.earningSales;var r=e.data.data.series,a=e.data.data.colors,n=e.data.data.dates;null===t.chart?(t.chart=new ApexCharts(t.$el.querySelector(".sales-reports-chart"),{series:r,chart:{height:350,type:"area",toolbar:{show:!1}},dataLabels:{enabled:!1},stroke:{curve:"smooth"},colors:a,xaxis:{type:"datetime",categories:n},tooltip:{x:{format:t.format}},noData:{text:BotbleVariables.languages.tables.no_data}}),t.chart.render()):t.chart.updateOptions({series:r,colors:a,xaxis:{type:"datetime",categories:n}})}})},clickFilter:function(t,e){var r=this;e.preventDefault(),this.setFiltering("...");var a=this;axios.get(a.url+"?date_from="+this.chartFromDate+"&date_to="+this.chartToDate,{params:{filter:t}}).then(function(e){if(e.data.error)Botble.showError(e.data.message);else{a.earningSales=e.data.data.earningSales;var n={xaxis:{type:"datetime",categories:e.data.data.dates},series:e.data.data.series};e.data.data.colors&&(n.colors=e.data.data.colors),r.chart.updateOptions(n)}r.setFiltering(t)})}}};var d=function r(a){var n=e[a];if(void 0!==n)return n.exports;var o=e[a]={exports:{}};return t[a](o,o.exports,r),o.exports}(6262);const f=(0,d.A)(u,[["render",function(t,e,u,d,f,h){return(0,r.openBlock)(),(0,r.createElementBlock)("div",null,[u.filters.length?((0,r.openBlock)(),(0,r.createElementBlock)("div",a,[(0,r.createElementVNode)("a",n,[e[0]||(e[0]=(0,r.createElementVNode)("i",{class:"fa fa-filter","aria-hidden":"true"},null,-1)),(0,r.createElementVNode)("span",null,(0,r.toDisplayString)(t.filtering),1),e[1]||(e[1]=(0,r.createElementVNode)("i",{class:"fa fa-angle-down"},null,-1))]),(0,r.createElementVNode)("ul",o,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(u.filters,function(t){return(0,r.openBlock)(),(0,r.createElementBlock)("li",{key:t.key},[(0,r.createElementVNode)("a",{href:"#",onClick:function(e){return h.clickFilter(t.key,e)}},(0,r.toDisplayString)(t.text),9,i)])}),128))])])):(0,r.createCommentVNode)("",!0),e[2]||(e[2]=(0,r.createElementVNode)("div",{class:"sales-reports-chart"},null,-1)),t.earningSales.length?((0,r.openBlock)(),(0,r.createElementBlock)("div",l,[(0,r.createElementVNode)("div",c,[(0,r.createElementVNode)("ul",s,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(t.earningSales,function(t){return(0,r.openBlock)(),(0,r.createElementBlock)("li",{key:t.text},[(0,r.createElementVNode)("i",{class:"icon ti ti-circle-filled",style:(0,r.normalizeStyle)({color:t.color})},null,4),(0,r.createTextVNode)(" "+(0,r.toDisplayString)(t.text),1)])}),128))])])])):(0,r.createCommentVNode)("",!0),e[3]||(e[3]=(0,r.createElementVNode)("div",{class:"loading"},null,-1))])}]]);var h={ref:"chartRef",class:"revenue-chart"};function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",n=r.toStringTag||"@@toStringTag";function o(r,a,n,o){var c=a&&a.prototype instanceof l?a:l,s=Object.create(c.prototype);return m(s,"_invoke",function(r,a,n){var o,l,c,s=0,u=n||[],d=!1,f={p:0,n:0,v:t,a:h,f:h.bind(t,4),d:function(e,r){return o=e,l=0,c=t,f.n=r,i}};function h(r,a){for(l=r,c=a,e=0;!d&&s&&!n&&e<u.length;e++){var n,o=u[e],h=f.p,p=o[2];r>3?(n=p===a)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=t):o[0]<=h&&((n=r<2&&h<o[1])?(l=0,f.v=a,f.n=o[1]):h<p&&(n=r<3||o[0]>a||a>p)&&(o[4]=r,o[5]=a,f.n=p,l=0))}if(n||r>1)return i;throw d=!0,a}return function(n,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&h(u,p),l=u,c=p;(e=l<2?t:c)||!d;){o||(l?l<3?(l>1&&(f.n=-1),h(l,c)):f.n=c:f.v=c);try{if(s=2,o){if(l||(n="next"),e=o[n]){if(!(e=e.call(o,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,l<2&&(l=0)}else 1===l&&(e=o.return)&&e.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+n+"' method"),l=1);o=t}else if((e=(d=f.n<0)?c:r.call(a,f))!==i)break}catch(e){o=t,l=1,c=e}finally{s=1}}return{value:e,done:d}}}(r,n,o),!0),s}var i={};function l(){}function c(){}function s(){}e=Object.getPrototypeOf;var u=[][a]?e(e([][a]())):(m(e={},a,function(){return this}),e),d=s.prototype=l.prototype=Object.create(u);function f(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,m(t,n,"GeneratorFunction")),t.prototype=Object.create(d),t}return c.prototype=s,m(d,"constructor",s),m(s,"constructor",c),c.displayName="GeneratorFunction",m(s,n,"GeneratorFunction"),m(d),m(d,n,"Generator"),m(d,a,function(){return this}),m(d,"toString",function(){return"[object Generator]"}),(p=function(){return{w:o,m:f}})()}function m(t,e,r,a){var n=Object.defineProperty;try{n({},"",{})}catch(t){n=0}m=function(t,e,r,a){if(e)n?n(t,e,{value:r,enumerable:!a,configurable:!a,writable:!a}):t[e]=r;else{var o=function(e,r){m(t,e,function(t){return this._invoke(e,r,t)})};o("next",0),o("throw",1),o("return",2)}},m(t,e,r,a)}function y(t,e,r,a,n,o,i){try{var l=t[o](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(a,n)}Vue.nextTick;const v={props:{data:{type:Array,default:function(){return[]},required:!0}},data:function(){return{chartData:this.data,chartInstance:null}},mounted:function(){var t=this;this.renderChart(),$event.on("revenue-chart:reload",function(e){t.chartData=e,t.renderChart()})},methods:{renderChart:function(){var t,e=this;return(t=p().m(function t(){var r,a,n,o;return p().w(function(t){for(;;)switch(t.n){case 0:if(e.chartData.length){t.n=1;break}return t.a(2);case 1:r=[],a=[],n=[],o=0,e.chartData.map(function(t){o+=parseFloat(t.value),n.push(t.label),a.push(t.color)}),0===o?e.chartData.map(function(){r.push(0)}):e.chartData.map(function(t){r.push(100/o*parseFloat(t.value))}),null===e.chartInstance?(e.chartInstance=new ApexCharts(e.$refs.chartRef,{series:r,colors:a,chart:{height:"250",type:"donut"},chartOptions:{labels:n},plotOptions:{pie:{donut:{size:"71%",polygons:{strokeWidth:0}},expandOnClick:!0}},states:{hover:{filter:{type:"darken",value:.9}}},dataLabels:{enabled:!1},legend:{show:!1},tooltip:{enabled:!1}}),e.chartInstance.render()):e.chartInstance.updateOptions({series:r,colors:a,chartOptions:{labels:n}}),jQuery&&jQuery().tooltip&&$('[data-bs-toggle="tooltip"]').tooltip({placement:"top",boundary:"window"});case 2:return t.a(2)}},t)}),function(){var e=this,r=arguments;return new Promise(function(a,n){var o=t.apply(e,r);function i(t){y(o,a,n,i,l,"next",t)}function l(t){y(o,a,n,i,l,"throw",t)}i(void 0)})})()}}},g=(0,d.A)(v,[["render",function(t,e,a,n,o,i){return(0,r.openBlock)(),(0,r.createElementBlock)("div",null,[(0,r.createElementVNode)("div",h,null,512)])}]]);function b(t){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function _(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=b(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==b(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}"undefined"!=typeof vueApp&&vueApp.booting(function(t){t.component("sales-reports-chart",f),t.component("revenue-chart",g)}),$(function(){if(window.moment&&jQuery().daterangepicker){moment.locale($("html").attr("lang"));var t=$(document).find(".date-range-picker"),e=t.data("format")||"YYYY-MM-DD",r=t.data("start-date")||moment().subtract(29,"days"),a=moment(),n=moment().endOf("month");n>a&&(n=a);var o=BotbleVariables.languages.reports,i=_(_(_(_(_(_({},o.today,[a,a]),o.this_week,[moment().startOf("week"),a]),o.last_7_days,[moment().subtract(6,"days"),a]),o.last_30_days,[moment().subtract(29,"days"),a]),o.this_month,[moment().startOf("month"),n]),o.this_year,[moment().startOf("year"),moment().endOf("year")]);t.daterangepicker({ranges:i,alwaysShowCalendars:!0,startDate:r,endDate:n,maxDate:n,opens:"left",drops:"auto",locale:{format:e},autoUpdateInput:!1},function(e,r,a){$.ajax({url:t.data("href"),data:{date_from:e.format("YYYY-MM-DD"),date_to:r.format("YYYY-MM-DD"),predefined_range:a},type:"GET",success:function(t){if(t.error)Botble.showError(t.message);else{if($("#report-stats-content").length)$(".widget-item").each(function(e,r){var a=$(r).prop("id");$("#".concat(a)).replaceWith($(t.data).find("#".concat(a)))});else{var a=new URL(window.location.href);a.searchParams.set("date_from",e.format("YYYY-MM-DD")),a.searchParams.set("date_to",r.format("YYYY-MM-DD")),history.pushState({urlPath:a.href},"",a.href),window.location.reload()}window.LaravelDataTables&&Object.keys(window.LaravelDataTables).map(function(t){var a=window.LaravelDataTables[t],n=new URL(a.ajax.url());n.searchParams.set("date_from",e.format("YYYY-MM-DD")),n.searchParams.set("date_to",r.format("YYYY-MM-DD")),a.ajax.url(n.href).load()})}},error:function(t){Botble.handleError(t)}})}),t.on("apply.daterangepicker",function(t,r){var a=$(this),n=a.data("format-value");n||(n="__from__ - __to__");var o=n.replace("__from__",r.startDate.format(e)).replace("__to__",r.endDate.format(e));a.find("span").text(o)})}})})();